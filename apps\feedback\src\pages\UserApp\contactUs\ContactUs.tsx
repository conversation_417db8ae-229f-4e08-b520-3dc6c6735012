import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@repo/ui/components/card';
import { Skeleton } from '@repo/ui/components/skeleton';
import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Button } from '@repo/ui/components/button';
import { useNavigate } from 'react-router';
import { USER_ROUTES } from '@/app.routes';
import axiosInstance from '@/lib/axios';

interface ContactInfo {
  id: string;
  fullName: string;
  email: string;
  phone: string;
  location: string;
  branch: string;
}

interface ClientInfo {
  contactText: string;
  contacts: ContactInfo[];
}

const ContactUs: React.FC = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [clientInfo, setClientInfo] = useState<ClientInfo>({
    contactText: '',
    contacts: []
  });

  useEffect(() => {
    fetchContactInfo();
  }, []);

  const fetchContactInfo = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch client information including contact text and contact list
      const response = await axiosInstance.get('/staff/customers/info/');
      
      setClientInfo({
        contactText: response.data.contact_verbiage || '',
        contacts: response.data.contact_list || []
      });
    } catch (err) {
      console.error('Error fetching contact info:', err);
      setError('Failed to load contact information');
    } finally {
      setIsLoading(false);
    }
  };

  // Process contact text to handle line breaks and links
  const processContactText = (text: string) => {
    if (!text) return [];
    
    return text.split(/[\r\n]+/g).map((line, index) => {
      // Simple URL detection and conversion to links
      const urlRegex = /(https?:\/\/[^\s]+)/g;
      const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
      
      let processedLine = line
        .replace(urlRegex, '<a href="$1" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline">$1</a>')
        .replace(emailRegex, '<a href="mailto:$1" class="text-blue-600 dark:text-blue-400 hover:underline">$1</a>');
      
      return (
        <p 
          key={index} 
          className="mb-2 text-gray-700 dark:text-gray-300"
          dangerouslySetInnerHTML={{ __html: processedLine }}
        />
      );
    });
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-4xl mx-auto">
          <Button
            variant="ghost"
            onClick={() => navigate(USER_ROUTES().dashboard.surveyList)}
            className="mb-4"
          >
            ← Back to Surveys
          </Button>
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => navigate(USER_ROUTES().dashboard.surveyList)}
            className="mb-4"
          >
            ← Back to Surveys
          </Button>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Contact Us</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Get in touch with our support team for any questions or assistance.
          </p>
        </div>

        {/* Contact Information */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            ) : (
              <div className="space-y-4">
                {clientInfo.contactText ? (
                  <div className="prose dark:prose-invert max-w-none">
                    {processContactText(clientInfo.contactText)}
                  </div>
                ) : (
                  <p className="text-gray-600 dark:text-gray-400">
                    No contact information available at this time.
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Contact Directory */}
        {clientInfo.contacts && clientInfo.contacts.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Contact Directory</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  {Array(3).fill(0).map((_, index) => (
                    <div key={index} className="border dark:border-gray-600 rounded-lg p-4">
                      <Skeleton className="h-5 w-1/3 mb-2" />
                      <Skeleton className="h-4 w-1/2 mb-1" />
                      <Skeleton className="h-4 w-1/4" />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b dark:border-gray-600">
                        <th className="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">Name</th>
                        <th className="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">Location</th>
                        <th className="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">Branch</th>
                        <th className="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">Phone</th>
                      </tr>
                    </thead>
                    <tbody>
                      {clientInfo.contacts.map((contact) => (
                        <tr key={contact.id} className="border-b dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="py-3 px-4 text-gray-900 dark:text-white">{contact.fullName}</td>
                          <td className="py-3 px-4 text-gray-600 dark:text-gray-400">{contact.location}</td>
                          <td className="py-3 px-4 text-gray-600 dark:text-gray-400">{contact.branch}</td>
                          <td className="py-3 px-4 text-gray-600 dark:text-gray-400">
                            {contact.phone && (
                              <a 
                                href={`tel:${contact.phone}`}
                                className="text-blue-600 dark:text-blue-400 hover:underline"
                              >
                                {contact.phone}
                              </a>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ContactUs;
