import React from 'react';
import { Button } from '@repo/ui/components/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@repo/ui/components/dialog';
import { ScrollArea } from '@repo/ui/components/scroll-area';
import { SurveyMeta, Section, Analytics, Question } from '@/services/upwardReviewService';
import { HelpCircle } from 'lucide-react';

interface SurveySidebarProps {
  meta: SurveyMeta | null;
  sections: Section[];
  currentSection: Section | null;
  analytics: Analytics;
  faqs: any[];
  isLoading: boolean;
  isLoadingQuestions: boolean;
  showFaqDialog: boolean;
  allQuestions?: Question[]; // Add all questions to calculate proper counts
  onSectionSelect: (section: Section) => void;
  onBack: () => void;
  onNext: () => void;
  onFaqDialogChange: (open: boolean) => void;
  getCompletionPercentage: () => number;
}

const SurveySidebar: React.FC<SurveySidebarProps> = ({
  meta,
  sections,
  currentSection,
  analytics,
  faqs,
  isLoading,
  isLoadingQuestions,
  showFaqDialog,
  allQuestions = [],
  onSectionSelect,
  onBack,
  onNext,
  onFaqDialogChange,
  getCompletionPercentage
}) => {
  return (
    <div className="w-48 bg-gray-100 dark:bg-gray-800 min-h-screen p-4 border-r dark:border-gray-700 flex-shrink-0">
      <Button
        variant="ghost"
        onClick={onBack}
        className="text-blue-600 dark:text-blue-400 hover:bg-blue-600 dark:hover:bg-blue-600 p-1"
      >
        ← Back
      </Button>
      <hr className='my-4 dark:border-gray-600' />

      {/* Survey End Date */}
      <div className="mb-6">
        <h3 className="text-sm font-normal text-gray-500 dark:text-gray-400 mb-1">Survey Ends On</h3>
        <p className="text-sm font-medium text-gray-800 dark:text-gray-200">
          {isLoading ? (
            <div className="h-4 w-32 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
          ) : (
            meta?.endDate ?
              new Date(meta.endDate).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              }) : 'Not set'
          )}
        </p>
      </div>

      {/* Feedback For */}
      <div className="mb-6">
        <h3 className="text-sm font-normal text-gray-500 dark:text-gray-400 mb-1">Feedback For</h3>
        <h2 className="text-base font-semibold text-gray-900 dark:text-white">
          {isLoading ? (
            <div className="h-5 w-40 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
          ) : (
            meta?.surveyFor || 'Unknown'
          )}
        </h2>
      </div>

      <hr className='my-4 dark:border-gray-600' />

      {/* Survey Status - Questions and Completed */}
      <div className="mb-6 space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-base font-semibold text-gray-900 dark:text-white">{analytics.total}</span>
          <span className="text-sm font-normal text-gray-500 dark:text-gray-400">Questions</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-base font-semibold text-green-600 dark:text-green-400">{analytics.completed}</span>
          <span className="text-sm font-normal text-gray-500 dark:text-gray-400">Completed</span>
        </div>
      </div>

      <hr className='my-4 dark:border-gray-600' />

      {/* Categories */}
      <div>
        <h3 className="text-sm font-normal text-gray-500 dark:text-gray-400 mb-2">Categories</h3>
        <div className="space-y-1">
          {isLoading ? (
            Array(3).fill(0).map((_, index) => (
              <div key={index} className="h-6 w-full bg-gray-300 dark:bg-gray-600 rounded animate-pulse mb-1"></div>
            ))
          ) : (
            sections.map((section) => {
              // Calculate actual questions left for this section using real question data
              const sectionQuestions = allQuestions.filter(q => q.section === section.id);
              const completedSectionQuestions = sectionQuestions.filter(q =>
                q.response && q.response.value !== undefined && q.response.value !== null && q.response.value !== ''
              );
              const questionsLeft = sectionQuestions.length - completedSectionQuestions.length;

              return (
                <div
                  key={section.id}
                  className={`p-2 rounded cursor-pointer text-sm ${currentSection?.id === section.id
                    ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 border border-blue-200 dark:border-blue-700'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                    }`}
                  onClick={() => onSectionSelect(section)}
                >
                  <div className="font-medium text-gray-900 dark:text-white">{section.title}</div>
                  {questionsLeft > 0 && (
                    <div className="text-xs font-normal text-green-600 dark:text-green-400 mt-1">
                      {questionsLeft} questions left
                    </div>
                  )}
                </div>
              );
            })
          )}
        </div>
      </div>

      {/* FAQ Button */}
      {faqs.length > 0 && (
        <>
          <hr className='my-4 dark:border-gray-600' />
          <Dialog open={showFaqDialog} onOpenChange={onFaqDialogChange}>
            <DialogTrigger asChild>
              <Button variant="outline" className="w-full text-sm">
                <HelpCircle className="mr-2 h-4 w-4" />
                FAQs
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[80vh]">
              <DialogHeader>
                <DialogTitle>Frequently Asked Questions</DialogTitle>
              </DialogHeader>
              <ScrollArea className="max-h-[60vh]">
                <div className="space-y-4">
                  {faqs.map((faq, index) => (
                    <div key={index} className="space-y-2">
                      <h4 className="font-medium">{faq.question}</h4>
                      <p className="text-sm text-muted-foreground">{faq.answer}</p>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </DialogContent>
          </Dialog>
        </>
      )}
    </div>
  );
};

export default SurveySidebar;
